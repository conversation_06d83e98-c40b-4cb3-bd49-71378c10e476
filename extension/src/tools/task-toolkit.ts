import { ToolDescription } from '~/types';

export const TASK_TOOLKIT_TOOLS: ToolDescription[] = [
  {
    name: 'TaskToolkit_finishTask',
    description: 'Finish the task with task result',
    parameters: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          description: 'The status of the task execution, must be "completed" or "error".',
        },
        output: {
          type: 'string',
          description: 'The output of the task execution.',
        },
      },
      required: ['status', 'output'],
    },
    returns: {
      type: 'null',
      description: 'No return value.',
    },
  },
];

export const TASK_DONE_MESSAGE = `You have successfully executed the task and stored the result to database.
Simply reply "Task Completed" and exit. Thank you!`;
