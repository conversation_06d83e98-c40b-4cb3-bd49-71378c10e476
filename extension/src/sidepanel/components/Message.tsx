import React, { useState, useEffect, useCallback, useMemo } from 'react';

import { processMarkdown } from '../../utils/markdown-processor';
import { ScreenshotResult } from '~/tools/web-toolkit';
import { VersionedMessage, db } from '~/storages/indexdb';
import RunIcon from '~/assets/icons/run.svg';
import DoneIcon from '~/assets/icons/done.svg';
import ErrorIcon from '~/assets/icons/error.svg';
import Reasoning from './Reasoning';
import TaskList from './TaskList';
import RenderScript from './RenderScript';
import { useLanguage } from '~/utils/i18n';
import { env } from '~/configs/env';
import {
  ChatStatus,
  TaskResult,
  TaskStatus,
  MessageContentPart,
  extractTextFromContent,
  parseMessageContent,
} from '@the-agent/shared';
import { TaskWithStatus } from '~/types/task';

interface Props {
  message: VersionedMessage;
  isLatestResponse?: boolean;
  status?: ChatStatus;
  workflowMode: boolean;
  taskId: string;
}

interface ToolMessageContent {
  success: boolean;
  data?: unknown;
  error?: string;
}

function areEqual(prevProps: Props, nextProps: Props) {
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.message.content === nextProps.message.content &&
    prevProps.message.role === nextProps.message.role &&
    prevProps.message.version === nextProps.message.version &&
    prevProps.message.reasoning === nextProps.message.reasoning
  );
}

const MessageComponent = React.memo(function MessageComponent({
  message,
  isLatestResponse,
  status,
  workflowMode,
  taskId,
}: Props) {
  const { getMessage } = useLanguage();
  const isUser = message?.role === 'user';
  const isError = message?.status === 'error';
  const isTool = message?.role === 'tool';

  const [copySuccess, setCopySuccess] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [tasks, setTasks] = useState<TaskWithStatus[]>([]);

  const text = useMemo(() => {
    return extractTextFromContent(message.content);
  }, [message]);

  // Function to infer task status based on the requirements
  const inferTaskStatus = useCallback(
    async (taskId: string): Promise<TaskStatus> => {
      // complete: when `message.tool_calls?.[0]` has `result`
      const { tool_call_result } = (message.metadata ?? {}) as {
        tool_call_result?: TaskResult[];
      };
      if (tool_call_result) {
        return 'completed';
      }

      // Check if there are messages for this task_id
      if (message.run_id) {
        const messagesForTask = await db.getMessagesByRunIdAndTaskId(message.run_id, taskId);

        // pending: when `message.tool_calls?.[0]` has not `result`, and has messages of the `task_id`
        if (messagesForTask.length > 0) {
          return 'pending';
        }
      }

      // waiting: when has no messages of the `task_id`
      return 'waiting';
    },
    [message]
  );

  // Load task statuses when message changes
  useEffect(() => {
    if (message.name === 'PlannerAgent_run') {
      const { tool_call_arguments } = (message.metadata ?? {}) as {
        tool_call_arguments?: string;
      };

      // Check if argumentsString is valid before parsing
      if (!tool_call_arguments) {
        console.warn('tool_call_arguments is null or undefined');
        return;
      }

      try {
        const argumentsJson = JSON.parse(tool_call_arguments);
        const taskList = argumentsJson.taskList;

        if (taskList && Array.isArray(taskList)) {
          const loadTaskStatuses = async () => {
            const tasksWithStatus: TaskWithStatus[] = await Promise.all(
              taskList.map(async (result: any) => ({
                task: {
                  id: result.id,
                  goal: result.id
                    .replace(/_/g, ' ')
                    .replace(/\b\w/g, (l: string) => l.toUpperCase()),
                  output: result.output || '',
                },
                status: await inferTaskStatus(result.id),
              }))
            );
            setTasks(tasksWithStatus);
          };

          loadTaskStatuses();
        }
      } catch (error) {
        console.error('Failed to parse PlannerAgent_run arguments:', error);
      }
    }
  }, [message, inferTaskStatus]);

  if (!message) {
    console.warn('Message component received null or undefined message');
    return null;
  }

  if (workflowMode && taskId !== message.task_id) {
    return null;
  }

  if (isUser && message.actor === 'system') {
    return null;
  }

  const handleCopy = () => {
    if (!text) return;

    navigator.clipboard
      .writeText(text)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error('Failed to copy text: ', err);
      });
  };

  const shouldShowCopyButton = () => {
    if (isError) return false;
    if (!text) return false;
    if (message.role === 'tool') return false;

    return (isLatestResponse && status === 'idle') || (isHovered && isUser);
  };

  const renderToolMessage = () => {
    if (message.role !== 'tool') return null;

    if (message.name === 'DevToolkit_render') {
      // console.debug('---[renderToolMessage] message', message);
      // TODO use real data to render script
      return <RenderScript />;
    }

    // Skip BrowserAgent_execute
    if (message.name === 'BrowserAgent_execute') {
      return null;
    }

    // Render PlannerAgent_run as task list
    if (message.name === 'PlannerAgent_run') {
      return <TaskList tasks={tasks} runId={message.run_id} />;
    }

    let status: 'running' | 'success' | 'error' = 'running';
    let parsedContent: ToolMessageContent | null = null;

    if (!message.content) {
      status = 'running';
    } else {
      try {
        parsedContent = JSON.parse(message.content) as ToolMessageContent;
        if (typeof parsedContent.success === 'boolean') {
          status = parsedContent.success ? 'success' : 'error';
        }
      } catch (e) {
        status = 'error';
        console.warn('Failed to parse message.content as JSON:', e);
      }
    }

    let icon = (
      <img
        src={RunIcon}
        alt="running"
        style={{
          marginRight: 8,
          width: 18,
          height: 18,
          verticalAlign: 'middle',
          animation: 'spin 1s linear infinite',
        }}
      />
    );
    let tip = getMessage('executing');
    let border = '1px solid #ccc';
    let bg = '#fff';

    if (status === 'success') {
      icon = (
        <img
          src={DoneIcon}
          alt="done"
          style={{ marginRight: 8, width: 18, height: 18, verticalAlign: 'middle' }}
        />
      );
      tip = getMessage('executed');
      border = '1.5px solid #55B610';
      bg = '#f3faed';
    } else if (status === 'error') {
      icon = (
        <img
          src={ErrorIcon}
          alt="error"
          style={{ marginRight: 8, width: 18, height: 18, verticalAlign: 'middle' }}
        />
      );
      tip = getMessage('error');
      border = '1.5px solid #D20D0D';
      bg = '#fef2f2';
    }

    return (
      <div style={{ display: 'flex', gap: 12, flexWrap: 'wrap', margin: '4px 0' }}>
        <div
          style={{
            border,
            borderRadius: '6px',
            padding: '5px 10px',
            fontSize: '13px',
            lineHeight: '1.5',
            display: 'flex',
            alignItems: 'center',
            background: bg,
            fontWeight: 500,
            minWidth: 0,
            maxWidth: 320,
          }}
        >
          {icon}
          <span style={{ fontWeight: 500 }}>{tip}</span>
          <span
            style={{
              display: 'inline-block',
              backgroundColor: '#fff',
              color: '#999',
              border: '1px solid #ccc',
              padding: '1px 6px',
              borderRadius: '4px',
              marginLeft: '10px',
              fontSize: '12px',
              maxWidth: 120,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {message.name?.replace('TabToolkit_', '').replace('WebToolkit_', '') || ''}
          </span>
          <style>{`
            @keyframes spin { 100% { transform: rotate(360deg); } }
          `}</style>
        </div>
      </div>
    );
  };

  const renderReasoning = () => {
    if (!message.reasoning) return null;
    return (
      <div style={{ marginBottom: 8 }}>
        <Reasoning reasoning={message.reasoning} finished={message.status === 'completed'} />
      </div>
    );
  };

  const renderErrorMessage = () => {
    if (message.status !== 'error' || !message.error) return null;

    return (
      <div
        style={{
          marginTop: '8px',
          display: 'flex',
          gap: '8px',
          alignItems: 'center',
        }}
      >
        <div
          style={{
            flex: 1,
            padding: '10px 14px',
            backgroundColor: '#fecaca',
            border: '1px solid #f87171',
            borderRadius: '12px',
            color: '#b91c1c',
            fontSize: '12px',
            lineHeight: '1.4',
          }}
        >
          {message.error === getMessage('insufficientCredits')
            ? getMessage('insufficientCreditsTip')
            : message.error}
        </div>

        {message.error === getMessage('insufficientCredits') && (
          <button
            onClick={e => {
              e.preventDefault();
              window.open(env.WEB_URL, '_blank');
            }}
            style={{
              padding: '10px 14px',
              backgroundColor: '#000',
              border: '1px solid #e5e7eb',
              borderRadius: '12px',
              color: '#fff',
              fontSize: '12px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s',
              whiteSpace: 'nowrap',
              flexShrink: 0,
            }}
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor = '#333';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor = '#000';
            }}
          >
            {getMessage('rechargeTip')}
          </button>
        )}
      </div>
    );
  };

  const renderContent = () => {
    const content = text || '';
    if (isUser) {
      // Use shared utility function to parse message content
      const parsedContent = parseMessageContent(message.content);
      if (Array.isArray(parsedContent)) {
        return (
          <div>
            {parsedContent.map((part: MessageContentPart, idx: number) => {
              if (part.type === 'text' && part.text) {
                return (
                  <div key={`t-${idx}`} style={{ whiteSpace: 'pre-wrap', marginBottom: 6 }}>
                    {part.text}
                  </div>
                );
              }
              if (part.type === 'image_url' && part.image_url?.url) {
                return (
                  <div key={`i-${idx}`} style={{ marginTop: 6 }}>
                    <img
                      src={part.image_url.url}
                      alt="Attachment"
                      style={{
                        maxWidth: '100%',
                        borderRadius: 8,
                        border: '1px solid #e5e7eb',
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                      }}
                    />
                  </div>
                );
              }
              return null;
            })}
          </div>
        );
      }
      return <div style={{ whiteSpace: 'pre-wrap' }}>{content || ''}</div>;
    }

    if (isError) {
      const htmlContent = processMarkdown(content);

      return (
        <>
          {renderReasoning()}
          {content && (
            <div
              style={{ width: '100%', overflow: 'auto' }}
              dangerouslySetInnerHTML={{
                __html: isTool ? content : htmlContent,
              }}
            />
          )}
        </>
      );
    }

    const htmlContent = processMarkdown(content);
    const screenshotResult = message.tool_calls?.find(
      tool => tool.function.name === 'WebToolkit_screenshot'
    )?.result;
    const screenshotUrl = (screenshotResult?.data as ScreenshotResult)?.url || null;

    const toolCallHint =
      message.role === 'tool' ? <div style={{ marginTop: 8 }}>{renderToolMessage()}</div> : null;

    return (
      <>
        {renderReasoning()}
        <div
          style={{ width: '100%', overflow: 'auto' }}
          dangerouslySetInnerHTML={{
            __html: isTool ? content : htmlContent,
          }}
        />
        {toolCallHint}
        {screenshotUrl && (
          <div style={{ marginTop: '12px' }}>
            <img
              src={screenshotUrl}
              alt="Screenshot"
              style={{
                maxWidth: '100%',
                borderRadius: '8px',
                border: '1px solid #e5e7eb',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              }}
            />
          </div>
        )}
      </>
    );
  };

  return (
    <div
      style={{
        marginBottom: isUser || isLatestResponse ? '30px' : '0',
        marginTop: isUser ? '30px' : '0',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
          marginLeft: isUser ? '15%' : '0',
          marginRight: !isUser && message.name !== 'DevToolkit_render' ? '15%' : '0',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: isUser ? 'flex-end' : 'flex-start',
            maxWidth: '100%',
            position: 'relative',
          }}
        >
          <div
            style={{
              display: 'inline-block',
              maxWidth: '100%',
              padding: isUser ? '10px 16px' : '3px 16px 3px 0',
              textAlign: 'left',
              fontSize: '14px',
              lineHeight: '1.5',
              backgroundColor: isUser ? '#f2f2f2' : 'transparent',
              borderRadius: '12px',
              boxShadow: isUser ? '0 1px 2px rgba(0, 0, 0, 0.05)' : 'none',
              color: '#333333',
              wordBreak: 'break-word',
            }}
          >
            {message.role === 'tool' ? renderToolMessage() : renderContent()}
          </div>

          {renderErrorMessage()}

          {shouldShowCopyButton() && (
            <button
              onClick={handleCopy}
              style={{
                position: 'absolute',
                bottom: '-30px',
                left: isUser ? 'auto' : '0',
                right: isUser ? '0' : 'auto',
                width: '30px',
                height: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: 'none',
                backgroundColor: 'transparent',
                padding: 0,
                borderRadius: '4px',
                cursor: 'pointer',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.2s',
                opacity: !isUser ? 1 : isHovered ? 1 : 0,
                pointerEvents: !isUser ? 'auto' : isHovered ? 'auto' : 'none',
              }}
              title="Copy to clipboard"
            >
              {copySuccess ? (
                <svg
                  style={{
                    width: '16px',
                    height: '16px',
                    color: '#059669',
                  }}
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="20 6 9 17 4 12" />
                </svg>
              ) : (
                <svg
                  style={{
                    width: '16px',
                    height: '16px',
                    color: '#6b7280',
                  }}
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" />
                </svg>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}, areEqual);

export default MessageComponent;
