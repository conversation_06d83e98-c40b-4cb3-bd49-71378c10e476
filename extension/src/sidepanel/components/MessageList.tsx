import { useLiveQuery } from 'dexie-react-hooks';
import { useEffect, useRef, useState } from 'react';
import { db } from '~/storages/indexdb';
import Message from './Message';
import Thinking from './Thinking';
import { getMessage } from '~/utils/i18n';
import { message as antdMessage } from 'antd';
import { handleApiError } from '~/utils/conversation';
import { createApiClient } from '~/services/api/client';
import { ChatStatus } from '@the-agent/shared';
import { Loading } from './Loading';
import { useUser } from '~/hooks/useUser';

interface MessageListProps {
  convId: number;
  workflowMode: boolean;
  workflowId?: string;
  taskId?: string;
  welcomeComponent: React.ReactNode;
  status: ChatStatus;
  hasBanner: boolean;
  top?: number;
  bottom?: number;
}

export default function MessageList(props: MessageListProps) {
  const { convId, workflowId, taskId, status, top, bottom } = props;

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [loading, setLoading] = useState(true);

  const user = useUser();
  const messages = useLiveQuery(
    () => (convId === -1 ? [] : db.getMessagesByConversation(convId, workflowId, taskId)),
    [convId, workflowId, taskId]
  );

  useEffect(() => {
    if (messages !== undefined) {
      setLoading(false);
    }
  }, [messages]);

  const lastMessageVersion =
    useLiveQuery(() => {
      if (convId === -1) return 0;
      return db.getLastMessageVersion(convId);
    }, [convId]) ?? 0;

  // auto scroll to the end of the message list
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages?.length, lastMessageVersion]);

  const shouldShowSaveToSiteButton = () => {
    if (status !== 'idle') return false;
    if (!messages || messages.length === 0) return false;
    const lastMessage = messages[messages.length - 1];
    if (lastMessage.role !== 'assistant') return false;
    if (!user?.permission?.can_save_site_message) return false;

    const hasUserOrToolMessage = messages.some(msg => msg.role === 'user' || msg.role === 'tool');
    if (!hasUserOrToolMessage) return false;

    return true;
  };

  const handleSaveToSite = async () => {
    if (!messages || isSaving) return;

    setIsSaving(true);
    try {
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });
      if (!activeTab?.url || activeTab.url.startsWith('chrome://')) {
        console.warn('Cannot save site message: invalid tab URL');
        antdMessage.warning('Cannot save for the current site.');
        return;
      }

      const messagesToProcess = messages.filter(msg => {
        if (msg.role === 'user') return true;
        if (msg.role === 'assistant') return true;
        if (msg.role === 'tool') {
          return (
            msg.tool_calls &&
            msg.tool_calls.length > 0 &&
            msg.tool_calls[0].result?.success === true
          );
        }
        return false;
      });

      if (messagesToProcess.length === 0) {
        antdMessage.info('No messages to save.');
        return;
      }

      // transform to new memory API format
      const hostname = new URL(activeTab.url).hostname;

      const memoryData = {
        messages,
        config: {
          filters: {
            hostname,
          },
          metadata: {
            hostname,
            memoryType: 'site',
          },
        },
      };

      const client = await createApiClient();
      await client.addMemory(memoryData);
      antdMessage.success('Saved to site memory successfully!');
    } catch (error) {
      console.error('Failed to save site messages:', error);
      antdMessage.error('Failed to save to site memory.');
      handleApiError(error, convId, getMessage);
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <div>
        <Loading />
      </div>
    );
  }

  return (
    <div
      style={{
        position: 'absolute',
        top: top ? `${top}px` : props.hasBanner ? '84px' : '44px',
        bottom: bottom ? `${bottom}px` : '90px',
        left: 0,
        right: 0,
        overflowY: 'auto',
        overflowX: 'hidden',
        backgroundColor: '#FFFFFF',
      }}
    >
      <div className="max-w-3xl mx-auto p-4">
        {(messages?.length ?? 0) === 0 ? (
          props.welcomeComponent
        ) : (
          <div style={{ paddingBottom: '32px' }}>
            {messages?.map((message, index) => {
              const isLast =
                (index === messages.length - 1 && message.role === 'assistant') ||
                message.status === 'error';

              return (
                <Message
                  key={`${message.id}-${message.version}-${isLast}-${status}`}
                  message={message}
                  isLatestResponse={isLast}
                  status={status}
                  workflowMode={props.workflowMode}
                  taskId="root"
                />
              );
            })}
            {status === 'running' && (
              <div style={{ padding: '16px 0', textAlign: 'center' }}>
                <Thinking />
              </div>
            )}
            {shouldShowSaveToSiteButton() && (
              <div style={{ display: 'flex', justifyContent: 'center', marginTop: '16px' }}>
                <button
                  onClick={handleSaveToSite}
                  disabled={isSaving}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    border: '1px solid #e5e7eb',
                    backgroundColor: '#f9fafb',
                    cursor: isSaving ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s ease',
                    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                    color: '#4b5563',
                    fontSize: '14px',
                    fontWeight: 500,
                    opacity: isSaving ? 0.7 : 1,
                  }}
                  onMouseOver={e => {
                    if (isSaving) return;
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                    e.currentTarget.style.borderColor = '#d1d5db';
                  }}
                  onMouseOut={e => {
                    if (isSaving) return;
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                    e.currentTarget.style.borderColor = '#e5e7eb';
                  }}
                  title={getMessage('saveToSiteMemory')}
                >
                  {isSaving ? (
                    <>
                      <style>{`
                          @keyframes spin { 100% { transform: rotate(360deg); } }
                        `}</style>
                      <svg
                        style={{
                          width: '16px',
                          height: '16px',
                          marginRight: '8px',
                          animation: 'spin 1s linear infinite',
                        }}
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M21 12a9 9 0 1 1-6.219-8.56"
                        />
                      </svg>
                      {getMessage('loading')}
                    </>
                  ) : (
                    <>
                      <svg
                        style={{
                          width: '16px',
                          height: '16px',
                          marginRight: '8px',
                        }}
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
                        <polyline points="17,21 17,13 7,13 7,21" />
                        <polyline points="7,3 7,8 15,8" />
                      </svg>
                      {getMessage('saveToSiteMemoryTitle')}
                    </>
                  )}
                </button>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
    </div>
  );
}
