import MurmurHash<PERSON> from 'imurmurhash';

import { TemplateRegisterRequest } from '@the-agent/shared';
import { SimpleTurndownService } from './SimpleTurndownService';
import {
  COMMON_TAGS,
  InteractionType,
  INTERACTIVE_ATTRIBUTES,
  INTERACTIVE_CLASS_PATTERNS,
  INTERACTIVE_DATA_ATTRIBUTES,
  INTERACTIVE_ELEMENTS,
  INTERACTIVE_ID_PATTERNS,
  INTERACTIVE_ROLES,
  INTERACTIVE_SELECTORS,
  POTENTIAL_INTERACTIVE_SELECTORS,
  SKIP_TAGS,
} from './constants';
import { WebInteractionResult } from '~/types/tools';
import {
  getAssociatedLabels,
  getElementAttributes,
  getElementDepth,
  getElementValue,
  getFormContext,
  getUniqueSelector,
  isElementChecked,
  isElementHidden,
} from './element';

export interface DomElementInfo {
  x: number;
  y: number;
  visible: boolean;
  text?: string;
  html: string;
}

// Define all necessary types and interfaces within the function scope
export interface DOMElementNode {
  element: Element;
  tagName: string;
  attributes: Record<string, string>;
  textContent?: string;
  id?: string;
  selector: string;
  rect?: DOMRect;
  isVisible?: boolean;
  isInViewport?: boolean;
}

export interface HighlightedElement extends Omit<DOMElementNode, 'element'> {
  element: Element;
  index: number;
  type: string;
  interactionType: InteractionType;
  depth: number;
  value?: string;
  placeholder?: string;
  ariaLabel?: string;
  role?: string;
  disabled?: boolean;
  required?: boolean;
  checked?: boolean;
  selected?: boolean;
  formId?: string;
  formName?: string;
  associatedLabels?: string[];
}

export interface DomSelector {
  type: 'css' | 'xpath';
  selector: string;
}

export interface SummarizeResult {
  content: string;
  url: string;
  title: string;
}

function extractTagVectorFromDOM(doc: Document): number[] {
  const counts = new Map<string, number>();

  for (const tag of COMMON_TAGS) {
    const count = doc.getElementsByTagName(tag).length;
    counts.set(tag, count);
  }

  // Normalize
  const max = Math.max(...counts.values());
  return COMMON_TAGS.map(tag => (counts.get(tag) || 0) / (max || 1));
}

function extractDomShingles(doc: Document): string[] {
  const shingles: string[] = [];
  const walker = doc.createTreeWalker(doc.body, NodeFilter.SHOW_ELEMENT, null);

  let node = walker.currentNode as Element;
  while (node) {
    const tag = node.tagName.toLowerCase();
    const parent = node.parentElement?.tagName.toLowerCase() || '';
    shingles.push(`${parent}>${tag}`);
    node = walker.nextNode() as Element;
  }
  return shingles;
}

function computeSimHash(shingles: string[], bits = 64): bigint {
  const vector = Array<number>(bits).fill(0);

  shingles.forEach(shingle => {
    const hash = new MurmurHash3(shingle).result();
    // mix lower bits into upper 64 if needed
    for (let i = 0; i < bits; i++) {
      vector[i] += (hash >>> i) & 1 ? 1 : -1;
    }
  });

  const fingerprint = vector.map(v => (v >= 0 ? '1' : '0')).join('');
  return BigInt('0b' + fingerprint);
}

export function buildDomTemplateFunction(): TemplateRegisterRequest {
  const domain = window.location.hostname;
  const path = window.location.pathname;
  const tag_vector = extractTagVectorFromDOM(document);
  const shingles = extractDomShingles(document);
  const simhash = computeSimHash(shingles);
  return {
    domain,
    path,
    tag_vector,
    simhash: Number(simhash),
  };
}

function getComputedStyle(element: Element): CSSStyleDeclaration | null {
  try {
    return window.getComputedStyle(element);
  } catch {
    return null;
  }
}

export function getInteractionType(element: Element): InteractionType | null {
  if (!element || element.nodeType !== Node.ELEMENT_NODE) {
    return null;
  }

  const tagName = element.tagName.toLowerCase();
  const style = getComputedStyle(element);

  if (INTERACTIVE_ELEMENTS[tagName]) {
    if ('disabled' in element && (element as HTMLInputElement).disabled) {
      return null;
    }
    if ('readOnly' in element && (element as HTMLInputElement).readOnly) {
      return null;
    }
    return INTERACTIVE_ELEMENTS[tagName];
  }

  // Check for interactive ARIA roles
  const role = element.getAttribute('role')?.toLowerCase();

  if (role && INTERACTIVE_ROLES[role]) {
    return INTERACTIVE_ROLES[role];
  }

  // Check for interactive cursor style
  if (style && style.cursor === 'pointer') {
    return 'click';
  }

  // Check for common event handlers
  for (const [attr, type] of INTERACTIVE_ATTRIBUTES) {
    if (element.hasAttribute(attr)) {
      return type;
    }
  }

  // Check for contenteditable
  if ((element as HTMLElement).isContentEditable) {
    return 'input';
  }

  // Check for tabindex
  const tabindex = element.getAttribute('tabindex');
  if (tabindex !== null && tabindex !== '-1') {
    return 'click';
  }

  // Check for interactive class patterns
  const className = element.className || '';
  for (const [pattern, type] of INTERACTIVE_CLASS_PATTERNS) {
    if (typeof className === 'string' && pattern.test(className)) {
      if (style) {
        if (
          style.cursor === 'pointer' ||
          style.userSelect === 'none' ||
          parseFloat(style.opacity) > 0.5
        ) {
          return type;
        }
      }

      const textContent = element.textContent?.trim();
      if (textContent || element.children.length > 0) {
        return type;
      }
    }
  }

  for (const [attr, type] of INTERACTIVE_DATA_ATTRIBUTES) {
    if (element.hasAttribute(attr)) {
      return type;
    }
  }

  // Check for specific patterns in id or data-testid
  const id = element.id || '';
  const testId = element.getAttribute('data-testid') || '';
  for (const [pattern, type] of INTERACTIVE_ID_PATTERNS) {
    if (id && pattern.test(id)) {
      return type;
    }
    if (testId && pattern.test(testId)) {
      return type;
    }
  }

  // Check for styled interactive elements
  if (style) {
    const hasBoxShadow = style.boxShadow && style.boxShadow !== 'none';
    const hasBorder = style.border && style.border !== 'none' && style.borderWidth !== '0px';
    const hasBackground =
      style.backgroundColor &&
      style.backgroundColor !== 'rgba(0, 0, 0, 0)' &&
      style.backgroundColor !== 'transparent';
    const hasTransition = style.transition && style.transition !== 'none';

    if ((hasBoxShadow || hasBorder || hasBackground) && hasTransition) {
      const textContent = element.textContent?.trim();
      if (textContent && textContent.length < 100) {
        return 'click';
      }
    }
  }
  return null;
}

export function findInteractiveElements(scopeElement?: Element): Map<Element, InteractionType> {
  const foundElements = new Map<Element, InteractionType>();
  const searchRoot = scopeElement || document;

  const selectors = Object.keys(INTERACTIVE_SELECTORS).join(',');
  const selectorElements = searchRoot.querySelectorAll(selectors);
  selectorElements.forEach(element => {
    const interactionType = getInteractionType(element);
    if (interactionType) {
      foundElements.set(element, interactionType);
    }
  });

  const potentialElements = searchRoot.querySelectorAll(POTENTIAL_INTERACTIVE_SELECTORS.join(','));
  potentialElements.forEach(element => {
    const interactionType = getInteractionType(element);
    const stype = getComputedStyle(element);
    if (stype && stype.cursor === 'pointer' && interactionType) {
      foundElements.set(element, interactionType);
    }
  });

  // Additional scan for elements with pointer cursor
  const allElements = searchRoot.querySelectorAll('*');
  allElements.forEach(element => {
    if (foundElements.has(element)) return;
    const interactionType = getInteractionType(element);
    if (interactionType) {
      foundElements.set(element, interactionType);
    }
  });
  return foundElements;
}

export function groupElementsBySemanticSegment(
  elements: HighlightedElement[]
): Record<string, HighlightedElement[]> {
  const grouped: Record<string, HighlightedElement[]> = {
    header: [],
    nav: [],
    main: [],
    section: [],
    article: [],
    aside: [],
    footer: [],
    other: [],
  };
  const semanticSelectors = ['header', 'nav', 'main', 'section', 'article', 'aside', 'footer'];
  const semanticElements = new Map<Element, string>();

  semanticSelectors.forEach(tagName => {
    const elements = document.querySelectorAll(tagName);
    elements.forEach(element => {
      semanticElements.set(element, tagName);
    });
  });

  elements.forEach(element => {
    let assigned = false;

    for (const [semanticElement, segmentType] of semanticElements.entries()) {
      if (element.element && semanticElement.contains(element.element)) {
        grouped[segmentType].push(element);
        assigned = true;
        break;
      }
    }
    if (!assigned) {
      grouped.other.push(element);
    }
  });

  return grouped;
}

export function analyzePageDOM(selector?: string): HighlightedElement[] {
  if (!document || document.readyState === 'loading') {
    throw new Error('Document not ready for analysis');
  }
  const highlightedElements: HighlightedElement[] = [];
  // Find scope element if fullXPath is provided
  let scopeElements: Element[] | undefined;
  if (selector) {
    try {
      scopeElements = Array.from(document.querySelectorAll(selector));
      if (!scopeElements || scopeElements.length === 0) {
        throw new Error(`Element with selector "${selector}" not found`);
      }
    } catch (error) {
      throw new Error(`Invalid selector or element not found: ${selector} ${error}`);
    }
  }

  const elements: Map<Element, InteractionType> = new Map();
  if (scopeElements && scopeElements.length > 0) {
    for (const scopeElement of scopeElements) {
      findInteractiveElements(scopeElement).forEach((type, e) => elements.set(e, type));
    }
  } else {
    findInteractiveElements().forEach((type, e) => elements.set(e, type));
  }

  let index = 0;
  elements.forEach((interactionType, element) => {
    if (highlightedElements.some(he => he.element === element)) {
      return;
    }

    // Skip unwanted elements before creating HighlightedElement
    if (SKIP_TAGS.has(element.tagName.toLowerCase())) {
      return;
    }

    const visible = !isElementHidden(element);
    if (!visible) {
      return;
    }

    const tagName = element.tagName.toLowerCase();
    const attributes = getElementAttributes(element);

    const associatedLabels = getAssociatedLabels(element);
    const { formId, formName } = getFormContext(element);

    highlightedElements.push({
      element: element,
      index: index++,
      depth: getElementDepth(element),
      tagName,
      type: attributes.type || tagName,
      interactionType,
      attributes,
      isVisible: visible,
      textContent: element.textContent?.trim() || undefined,
      id: element.id || undefined,
      selector: getUniqueSelector(element),
      value: getElementValue(element),
      placeholder: attributes.placeholder,
      ariaLabel: attributes['aria-label'],
      role: attributes.role,
      disabled: element.hasAttribute('disabled') || attributes['aria-disabled'] === 'true',
      required: element.hasAttribute('required') || attributes['aria-required'] === 'true',
      checked: isElementChecked(element),
      selected: element.hasAttribute('selected') || attributes['aria-selected'] === 'true',
      formId,
      formName,
      associatedLabels: associatedLabels.length > 0 ? associatedLabels : undefined,
    });
  });
  return highlightedElements;
}

export type TreeNode = HighlightedElement & { children?: TreeNode[] };

export function buildElementTree(elements: HighlightedElement[]): TreeNode[] {
  const elMap = new Map<Element, TreeNode>();
  const roots: TreeNode[] = [];

  // Wrap each HighlightedElement as a TreeNode and index by DOM element
  for (const el of elements) {
    elMap.set(el.element, { ...el, children: [] });
  }

  for (const node of elMap.values()) {
    let current = node.element.parentElement;
    let foundParent: TreeNode | undefined = undefined;

    while (current) {
      const potentialParent = elMap.get(current);
      if (potentialParent) {
        foundParent = potentialParent;
        break;
      }
      current = current.parentElement;
    }

    if (foundParent) {
      foundParent.children!.push(node);
    } else {
      roots.push(node);
    }
  }

  return roots;
}

// Example output:
// - [1] form [action="/login", method="post"]
//   - [2] input [label="Username (Enter Username)", type="text", required]
//   - [3] button [label="Login", role="button", action="click"]

function isObviousInteractive(el: TreeNode): boolean {
  if (INTERACTIVE_ELEMENTS[el.tagName.toLowerCase()]) {
    return true;
  }
  if (el.role && INTERACTIVE_ROLES[el.role.toLowerCase()]) {
    return true;
  }
  return false;
}

function cleanUpLabel(label: string): string {
  const lines = label.split('\n');
  const cleanedLines = lines.map(line => line.trim()).filter(line => line.length > 0);
  return cleanedLines.join(', ');
}

function formatElementLine(el: TreeNode): string | undefined {
  // Base line with tag name
  const line = `- [${el.index}] ${el.tagName.toLowerCase()}`;

  // Build attributes summary
  const attrList: string[] = [];

  if (!isObviousInteractive(el)) {
    attrList.push(`action="${el.interactionType}"`);
  }

  let hasSemanticAttributes = false;
  if (el.ariaLabel || el.placeholder || el.textContent) {
    const label = el.ariaLabel || el.placeholder || el.textContent?.slice(0, 50).trim();
    if (label) {
      const labelText = `label="${cleanUpLabel(label)}"`;
      if (el.associatedLabels?.length) {
        const associatedLabels = cleanUpLabel(el.associatedLabels?.join(', '));
        attrList.push(`${labelText} (${associatedLabels})"`);
      } else {
        attrList.push(labelText);
      }
      hasSemanticAttributes = true;
    }
  }

  if (el.value) {
    attrList.push(`value="${el.value.slice(0, 50).trim()}"`);
    hasSemanticAttributes = true;
  }
  if (el.formName) {
    attrList.push(`formName="${el.formName}"`);
    hasSemanticAttributes = true;
  }

  if (el.type !== el.tagName) attrList.push(`type="${el.type}"`);
  if (el.role) attrList.push(`role="${el.role}"`);
  if (el.disabled) attrList.push('disabled');
  if (el.checked) attrList.push('checked');

  if (hasSemanticAttributes) {
    return `${line} [${attrList.join(', ')}]`;
  }
}

function formatElementTree(tree: TreeNode[], depth: number = 0): string {
  const lines: string[] = [];

  for (const node of tree) {
    const indent = '  '.repeat(depth); // 2-space indent
    const line = formatElementLine(node);
    if (line) {
      lines.push(`${indent}${line.trim()}`);
    }

    if (node.children && node.children.length > 0) {
      const childrenLines = formatElementTree(node.children, depth + 1).trimEnd();
      if (childrenLines.length > 0) {
        lines.push(childrenLines);
      }
    }
  }

  return lines.join('\n');
}

export function formatHighlightedElements(elements: HighlightedElement[]): string {
  if (elements.length === 0) {
    return 'No interactive elements found on this page.';
  }

  const tree = buildElementTree(elements);
  return formatElementTree(tree);
}

/**
 * Returns the markdown conversion function to be executed in the tab context
 */
export function getMarkdownConversionFunction(): SummarizeResult {
  try {
    const turndownService = new SimpleTurndownService();

    let html: string;
    try {
      html = document.documentElement.outerHTML;
    } catch {
      html = document.body?.outerHTML || '';
    }

    const markdown = turndownService.turndown(html);

    return {
      content: markdown.trim(),
      url: window.location.href,
      title: document.title,
    };
  } catch {
    // Fallback to plain text extraction
    const text =
      document.body?.innerText ||
      document.documentElement.innerText ||
      'Error extracting page text';

    return {
      content: text,
      url: window.location.href,
      title: document.title,
    };
  }
}

// Use Symbol to avoid conflicts with page scripts
const ELEMENT_INDEX_MAP_KEY = Symbol('__elementIndexMap__');

// Safe way to access element index map
function getElementIndexMap(): Record<number, string> | undefined {
  return (window as any)[ELEMENT_INDEX_MAP_KEY];
}

function setElementIndexMap(map: Record<number, string>): void {
  (window as any)[ELEMENT_INDEX_MAP_KEY] = map;
}

function resolveSelector(selectorOrIndex: string): {
  selector: string;
  element?: HTMLElement;
  error?: string;
} {
  let selector = selectorOrIndex;
  if (/^\d+$/.test(selectorOrIndex)) {
    const index = parseInt(selectorOrIndex);
    const elementMap = getElementIndexMap();
    if (!elementMap || !elementMap[index]) {
      return { selector: '', error: `No element found at index ${index}` };
    }
    selector = elementMap[index];
    if (!selector) {
      return { selector: '', error: `No element found at index ${index}` };
    }
  }

  let element: HTMLElement | null = null;

  const selectors = [
    selector,
    selector.includes('[label=')
      ? selector.replace(/\[label="([^"]+)"\]/, '[aria-label="$1"]')
      : null,
    selector.includes('[role=') ? selector.replace(/\[role="([^"]+)"\]/, '[data-role="$1"]') : null,
    // Try without attribute selectors if they fail
    selector.includes('[') ? selector.replace(/\[[^\]]+\]/g, '') : null,
    // Try common fallback selectors for input elements
    selector.includes('data-testid') ? selector.replace(/\[data-testid="[^"]+"\]/, '') : null,
    selector.includes('data-testid')
      ? selector.replace(/\[data-testid="[^"]+"\]/, '[contenteditable="true"]')
      : null,
  ].filter(Boolean) as string[];

  for (const currentSelector of selectors) {
    try {
      if (currentSelector.startsWith('/')) {
        // XPath selector
        const result = document.evaluate(
          currentSelector,
          document,
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null
        );
        element = result.singleNodeValue as HTMLElement | null;
      } else {
        element = document.querySelector(currentSelector);
      }

      if (element) {
        return { selector: currentSelector, element: element as HTMLElement };
      }
    } catch (selectorError) {
      console.warn(`Selector "${currentSelector}" failed:`, selectorError);
    }
  }

  // If we get here, no selector worked
  const errorMessage = `Element with selector "${selector}" not found. Tried: ${selectors.join(', ')}`;

  // Provide additional debugging info
  const similarElements = document.querySelectorAll(
    'input, textarea, [contenteditable], [role="textbox"], [role="searchbox"], [role="combobox"]'
  );
  if (similarElements.length > 0) {
    const suggestions = Array.from(similarElements)
      .slice(0, 5)
      .map(el => {
        const tag = el.tagName.toLowerCase();
        const role = el.getAttribute('role');
        const testId = el.getAttribute('data-testid');
        const label = el.getAttribute('aria-label') || el.getAttribute('label');
        const placeholder = el.getAttribute('placeholder');

        let suggestion = tag;
        if (testId) suggestion += `[data-testid="${testId}"]`;
        if (role) suggestion += `[role="${role}"]`;
        if (label) suggestion += `[aria-label="${label}"]`;
        if (placeholder) suggestion += `[placeholder="${placeholder}"]`;

        return suggestion;
      });
    return {
      selector,
      error: `${errorMessage}. Available input elements: ${suggestions.join(', ')}`,
    };
  }

  return { selector, error: errorMessage };
}

async function clickElement(selectorOrIndex: string): Promise<WebInteractionResult<string>> {
  try {
    const { element, error } = resolveSelector(selectorOrIndex);
    if (error) {
      return { success: false, error: error };
    }
    if (!element) {
      return { success: false, error: 'Element not found' };
    }

    // Check if element is visible
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      return { success: false, error: 'Element is not visible or has zero dimensions' };
    }

    // Check if element is disabled
    if (element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true') {
      return { success: false, error: 'Element is disabled' };
    }

    // Scroll element into view
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Normal click for other pages
    element.click();

    return {
      success: true,
      data: `Success: Clicked element "${element.tagName.toLowerCase()}" with text "${element.textContent?.trim() || 'no text'}"`,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function inputElement(
  selectorOrIndex: string,
  value: string,
  clearFirst: boolean = false,
  pressEnterAfterInput: boolean = false
): Promise<WebInteractionResult<string>> {
  try {
    const { element, error } = resolveSelector(selectorOrIndex);
    if (error) {
      return { success: false, error: error };
    }
    if (!element) {
      return { success: false, error: 'Element not found' };
    }

    // Generic function to check if element is input-capable
    const isInputCapable = (el: Element): boolean => {
      const tagName = el.tagName.toLowerCase();
      const role = el.getAttribute('role');
      const contentEditable = el.getAttribute('contenteditable');

      // Check tag-based input elements
      if (['input', 'textarea', 'select'].includes(tagName)) return true;

      // Check role-based input elements
      if (['textbox', 'searchbox', 'combobox'].includes(role || '')) return true;

      // Check contenteditable elements
      if (contentEditable === 'true') return true;

      return false;
    };

    // Check if element is input-capable
    if (!isInputCapable(element)) {
      const tagName = element.tagName.toLowerCase();
      const role = element.getAttribute('role') || 'none';
      return {
        success: false,
        error: `Element is not input-capable. Found: ${tagName} with role: ${role}`,
      };
    }

    // Check if element is visible
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      return { success: false, error: 'Element is not visible or has zero dimensions' };
    }

    // Scroll element into view
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Focus the element first
    element.focus();
    element.dispatchEvent(new Event('focus', { bubbles: true, cancelable: true }));
    await new Promise(resolve => setTimeout(resolve, 50));

    // Clear existing value if requested
    if (clearFirst) {
      // For contenteditable elements, clear using backspace simulation
      if (element.getAttribute('contenteditable') === 'true') {
        element.textContent = '';
        element.innerHTML = '';
      } else if (
        element.tagName.toLowerCase() === 'input' ||
        element.tagName.toLowerCase() === 'textarea'
      ) {
        const inputEl = element as HTMLInputElement | HTMLTextAreaElement;
        const currentValue = inputEl.value;
        for (let i = 0; i < currentValue.length; i++) {
          // Simulate backspace key events
          const backspaceDown = new KeyboardEvent('keydown', {
            key: 'Backspace',
            code: 'Backspace',
            bubbles: true,
            cancelable: true,
            composed: true,
          });
          element.dispatchEvent(backspaceDown);

          const backspaceUp = new KeyboardEvent('keyup', {
            key: 'Backspace',
            code: 'Backspace',
            bubbles: true,
            cancelable: true,
            composed: true,
          });
          element.dispatchEvent(backspaceUp);

          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }

      // Trigger events for clearing
      element.dispatchEvent(
        new Event('input', { bubbles: true, cancelable: true, composed: true })
      );
      element.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // Use a more realistic input method - simulate typing
    if (value) {
      // Method 1: Try using execCommand for contenteditable elements
      if (element.getAttribute('contenteditable') === 'true') {
        // For contenteditable, use document.execCommand
        element.focus();
        document.execCommand('insertText', false, value);
      } else {
        // For regular input elements, simulate character-by-character typing
        for (let i = 0; i < value.length; i++) {
          const char = value[i];

          // Key down
          const keyDownEvent = new KeyboardEvent('keydown', {
            key: char,
            code: `Key${char.toUpperCase()}`,
            bubbles: true,
            cancelable: true,
            composed: true,
          });
          element.dispatchEvent(keyDownEvent);

          // Input event
          const inputEvent = new InputEvent('input', {
            data: char,
            bubbles: true,
            cancelable: true,
            composed: true,
          });
          element.dispatchEvent(inputEvent);

          // Key up
          const keyUpEvent = new KeyboardEvent('keyup', {
            key: char,
            code: `Key${char.toUpperCase()}`,
            bubbles: true,
            cancelable: true,
            composed: true,
          });
          element.dispatchEvent(keyUpEvent);

          // Small delay between characters
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }
    }

    // Wait for events to process
    await new Promise(resolve => setTimeout(resolve, 100));

    // Trigger comprehensive events to ensure button activation
    const events = [
      'input',
      'change',
      'keyup',
      'keydown',
      'paste',
      'cut',
      'focusin',
      'focusout',
      'DOMSubtreeModified',
      'DOMNodeInserted',
    ];

    events.forEach(eventType => {
      element.dispatchEvent(
        new Event(eventType, { bubbles: true, cancelable: true, composed: true })
      );
    });

    // Final input event with complete value
    const finalInputEvent = new InputEvent('input', {
      data: value,
      bubbles: true,
      cancelable: true,
      composed: true,
    });
    element.dispatchEvent(finalInputEvent);

    // Press Enter if requested
    if (pressEnterAfterInput) {
      const enterDownEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13,
        bubbles: true,
        cancelable: true,
        composed: true,
      });
      element.dispatchEvent(enterDownEvent);

      const enterUpEvent = new KeyboardEvent('keyup', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13,
        bubbles: true,
        cancelable: true,
        composed: true,
      });
      element.dispatchEvent(enterUpEvent);

      // Wait a bit for the Enter event to be processed
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Get element type description
    const getElementType = (el: Element): string => {
      const tagName = el.tagName.toLowerCase();
      const role = el.getAttribute('role');
      const contentEditable = el.getAttribute('contenteditable');

      if (tagName === 'select') return 'select';
      if (contentEditable === 'true') return 'contenteditable';
      if (role === 'textbox') return 'textbox';
      if (role === 'searchbox') return 'searchbox';
      if (role === 'combobox') return 'combobox';
      return 'input';
    };

    return {
      success: true,
      data: `Success: Input "${value}" into element "${element.tagName.toLowerCase()}" (${getElementType(element)})`,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

function scrollToElement(selectorOrIndex: string): WebInteractionResult<string> {
  try {
    const { element, error } = resolveSelector(selectorOrIndex);
    if (error) {
      return { success: false, error: error };
    }
    if (!element) {
      return { success: false, error: 'Element not found' };
    }

    // Scroll element into view
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });

    return {
      success: true,
      data: `Success: Scrolled to element "${element.tagName.toLowerCase()}" with text "${element.textContent?.trim() || 'no text'}"`,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

function getElement(selectorOrIndex: string): WebInteractionResult<DomElementInfo> {
  try {
    const { element, error } = resolveSelector(selectorOrIndex);
    if (error) {
      return { success: false, error: error };
    }
    if (!element) {
      return { success: false, error: 'Element not found' };
    }
    return {
      success: true,
      data: {
        x: element.getBoundingClientRect().left,
        y: element.getBoundingClientRect().top,
        visible:
          element.getBoundingClientRect().width > 0 && element.getBoundingClientRect().height > 0,
        html: element.outerHTML,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Message listeners for content script communication
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  try {
    // Security check: verify the message comes from our extension's background script
    if (!sender || sender.id !== chrome.runtime.id) {
      console.warn('Message received from unauthorized sender:', sender);
      sendResponse({
        success: false,
        error: 'Unauthorized sender',
      });
      return true;
    }

    switch (request.name) {
      case 'build-dom-template':
        const result = buildDomTemplateFunction();
        sendResponse({ success: true, data: result });
        break;

      case 'build-dom-tree':
        const selector = request.selector;
        const elements = analyzePageDOM(selector);
        setElementIndexMap(
          elements.reduce(
            (acc, el) => {
              acc[el.index] = el.selector;
              return acc;
            },
            {} as Record<number, string>
          )
        );
        const formatted = formatHighlightedElements(elements);
        sendResponse({ success: true, data: formatted });
        break;

      case 'summarize-page':
        const summaryResult = getMarkdownConversionFunction();
        sendResponse({ success: true, data: summaryResult });
        break;

      case 'click-element':
        // Handle async clickElement function
        clickElement(request.selectorOrIndex)
          .then(result => sendResponse(result))
          .catch(error =>
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            })
          );
        return true; // Keep message channel open for async response

      case 'input-element':
        // Handle async inputElement function
        inputElement(
          request.selectorOrIndex,
          request.value,
          request.clearFirst,
          request.pressEnterAfterInput
        )
          .then(result => sendResponse(result))
          .catch(error =>
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            })
          );
        return true; // Keep message channel open for async response

      case 'scroll-to-element':
        const scrollResult = scrollToElement(request.selectorOrIndex);
        sendResponse(scrollResult);
        break;

      case 'get-element':
        const elementResult = getElement(request.selectorOrIndex);
        sendResponse(elementResult);
        break;

      default:
        sendResponse({
          success: false,
          error: `Unknown request type: ${request.name}`,
        });
        break;
    }
    return true;
  } catch (error) {
    console.error('Error during DOM analysis:', error);
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});
