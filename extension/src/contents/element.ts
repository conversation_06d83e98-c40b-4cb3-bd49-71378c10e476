export function getElementAttributes(element: Element): Record<string, string> {
  const attributes: Record<string, string> = {};
  for (let i = 0; i < element.attributes.length; i++) {
    const attr = element.attributes[i];
    attributes[attr.name] = attr.value;
  }
  return attributes;
}

export function getElementValue(element: Element): string | undefined {
  if ('value' in element) {
    return (element as HTMLInputElement).value || undefined;
  }
  return element.getAttribute('value') || undefined;
}

export function getFormContext(element: Element): { formId?: string; formName?: string } {
  let formId: string | undefined;
  let formName: string | undefined;

  if ('form' in element) {
    const form = (element as HTMLInputElement).form;
    if (form) {
      formId = form.id || undefined;
      formName = form.getAttribute('name') || undefined;
    }
  } else {
    let parent = element.parentElement;
    while (parent && parent !== document.body) {
      if (parent.tagName === 'FORM') {
        formId = parent.id || undefined;
        formName = parent.getAttribute('name') || undefined;
        break;
      }
      parent = parent.parentElement;
    }
  }

  return { formId, formName };
}

function getElementXPath(element: Node): string {
  if (element.nodeType !== Node.ELEMENT_NODE) return '';
  const parts: string[] = [];

  while (element && element.nodeType === Node.ELEMENT_NODE) {
    let index = 1;
    let sibling = element.previousSibling;

    while (sibling) {
      if (sibling.nodeType === Node.ELEMENT_NODE && sibling.nodeName === element.nodeName) {
        index++;
      }
      sibling = sibling.previousSibling;
    }

    const tagName = (element as Element).tagName.toLowerCase();
    const part = index > 1 ? `${tagName}[${index}]` : tagName;
    parts.unshift(part);

    element = element.parentNode!;
  }

  return '/' + parts.join('/');
}

export function isElementChecked(element: Element): boolean {
  if ('checked' in element) {
    return (element as HTMLInputElement).checked;
  }
  return element.getAttribute('aria-checked') === 'true';
}

export function getAssociatedLabels(element: Element): string[] {
  const labels: string[] = [];

  if (['input', 'select', 'textarea'].includes(element.tagName.toLowerCase())) {
    if (element.id) {
      const labelElements = document.querySelectorAll(`label[for="${element.id}"]`);
      labelElements.forEach(label => {
        if (label.textContent) {
          labels.push(label.textContent.trim());
        }
      });
    }
    let parent = element.parentElement;
    while (parent && parent !== document.body) {
      if (parent.tagName === 'LABEL' && parent.textContent) {
        labels.push(parent.textContent.trim());
        break;
      }
      parent = parent.parentElement;
    }
  }
  return labels.map(label => label.trim()).filter(label => label.length > 0);
}

// Utility functions
export function isElementHidden(element: Element): boolean {
  const style = element.getAttribute('style');
  const computedStyle = getComputedStyle(element);

  return (
    element.hasAttribute('hidden') ||
    (element.hasAttribute('aria-hidden') && element.getAttribute('aria-hidden') === 'true') ||
    (style != null &&
      (style.includes('display: none') ||
        style.includes('display:none') ||
        style.includes('visibility: hidden') ||
        style.includes('visibility:hidden'))) ||
    (computedStyle != null &&
      (computedStyle.display === 'none' ||
        computedStyle.visibility === 'hidden' ||
        computedStyle.opacity === '0'))
  );
}

export function getElementDepth(el: Element): number {
  let depth = 0;
  let current = el.parentElement;

  while (current && current !== document.body && current !== document.documentElement) {
    depth++;
    current = current.parentElement;
  }

  return depth;
}

function getUniqueCssSelector(el: Element): string | null {
  if (!el || el.nodeType !== Node.ELEMENT_NODE) return '';

  const isUnique = (selector: string) => document.querySelectorAll(selector).length === 1;

  // Try ID (fastest and usually unique)
  if (el.id && isUnique(`#${CSS.escape(el.id)}`)) {
    return `#${CSS.escape(el.id)}`;
  }

  // Try data-testid
  const testId = el.getAttribute('data-testid');
  if (testId && isUnique(`[data-testid="${testId}"]`)) {
    return `[data-testid="${testId}"]`;
  }

  // Try name
  const name = el.getAttribute('name');
  if (name && isUnique(`[name="${name}"]`)) {
    return `[name="${name}"]`;
  }

  // Try unique class
  if (el.classList.length === 1) {
    const className = el.classList[0];
    if (isUnique(`.${CSS.escape(className)}`)) {
      return `.${CSS.escape(className)}`;
    }
  }

  return null;
}

export function getUniqueSelector(element: Element): string {
  const cssSelector = getUniqueCssSelector(element);
  if (cssSelector) {
    return cssSelector;
  }
  return getElementXPath(element);
}
