import OpenAI from 'openai';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  Agent,
  AgentConfig,
  ToolCallResult,
  ToolCall,
  Task,
  TaskNode,
  TaskSchema,
  ToolOptions,
  TaskResult,
} from '@the-agent/shared';
import { GlobalContext } from '~/types/task';
import { TaskContextBuilder } from './context';
import { DEFAULT_MAX_DEPTH, DEFAULT_MAX_TOOL_CALLS } from '~/configs/common';
import { TaskToolExecutor } from '~/tools/task-executor';
import { ToolDescription } from '~/types';
import { MystaTaskNode } from './node';
import { TASK_TOOLKIT_TOOLS } from '~/tools/task-toolkit';

class PlannerAgentToolExecutor extends TaskToolExecutor {
  constructor(c: GlobalContext) {
    super(c);
  }

  async executeInternal(
    task: TaskNode,
    toolName: string,
    params: any,
    toolOptions: ToolOptions
  ): Promise<ToolCallResult> {
    switch (toolName) {
      case 'PlannerAgent_run':
        const p = params as { tasks: string };
        return await this.planAndRun(task, p.tasks, toolOptions);
      case 'BrowserAgent_run':
        return await this.run(task);
      default:
        return {
          success: false,
          error: 'Invalid tool call',
        };
    }
  }

  getToolDescriptions(): ToolDescription[] {
    return [
      {
        name: 'PlannerAgent_run',
        description: 'Record the list of atomic subtasks derived from a complex task.',
        parameters: {
          type: 'object',
          properties: {
            tasks: {
              type: 'string',
              description: 'The task list to run, must be more than 1 task.',
            },
          },
          required: ['tasks'],
        },
        returns: {
          type: 'object',
          description: 'No return value.',
          properties: {
            success: {
              type: 'boolean',
              description: 'if the toolcall success or not',
            },
            data: {
              type: 'string',
              description: '"done" for success call and the detailed error message for failed call',
            },
          },
        },
      },
      {
        name: 'BrowserAgent_run',
        description:
          'call this when the task is already atomic and the planner has decided **no further breakdown is needed**',
        returns: {
          type: 'object',
          description: 'No return value.',
          properties: {
            success: {
              type: 'boolean',
              description: 'if the toolcall success or not',
            },
            data: {
              type: 'string',
              description: '"done" for success call and the detailed error message for failed call',
            },
          },
        },
      },
      ...TASK_TOOLKIT_TOOLS,
    ];
  }

  getPostToolcallMessageInternal(toolCall: ToolCall): string {
    if (toolCall.function.name === 'PlannerAgent_run') {
      return toolCall.result?.success ? PLANNER_RUN_SUCCESS_MESSAGE : PLANNER_RUN_FAILED_MESSAGE;
    } else if (toolCall.function.name === 'BrowserAgent_run') {
      return toolCall.result?.success ? BROWSER_RUN_SUCCESS_MESSAGE : BROWSER_RUN_FAILED_MESSAGE;
    } else {
      throw new Error(`Invalid tool call ${toolCall.function.name}`);
    }
  }

  private async run(task: TaskNode): Promise<ToolCallResult> {
    task.atomic = true;
    const result = await task.run();
    return {
      success: result.status === 'completed',
      data: result.output ?? 'no result returned',
    };
  }

  private async planAndRun(
    task: TaskNode,
    tasks: string,
    toolOptions: ToolOptions
  ): Promise<ToolCallResult> {
    let subtasks: Task[] = [];
    try {
      subtasks = validateTasks(tasks);
    } catch (e) {
      return {
        success: false,
        error: `Failed to parse the task list with error ${e instanceof Error ? e.message : String(e)}`,
      };
    }

    if (subtasks.length === 0) {
      return {
        success: false,
        error: 'No subtasks to run',
      };
    }
    for (const subtask of subtasks) {
      const node = new MystaTaskNode(this.c, subtask, {
        parent: task,
        id: `${task.id}.${subtask.id}`,
      });
      task.nested_tasks.push(node);
    }
    const onTaskComplete = (results: TaskResult[]) => {
      toolOptions?.onToolMessageUpdate?.(results);
    };
    const result = await task.run(onTaskComplete);
    return {
      success: result.status === 'completed',
      data: result.output,
    };
  }
}

export function validateTasks(tasks: string): Task[] {
  const results: Task[] = [];
  const parsed = JSON.parse(tasks);
  if (!Array.isArray(parsed)) {
    throw new Error('Cannot parse the result as JSON array');
  }
  if (parsed.length === 0) {
    throw new Error('Empty taks list');
  }
  for (const t of parsed) {
    if (typeof t !== 'object') {
      throw new Error(`Invalid task object ${JSON.stringify(t)}`);
    }
    const task = TaskSchema.safeParse(t);
    if (task.success) {
      results.push(task.data);
    } else {
      throw new Error(`Failed to parse task ${JSON.stringify(t)} with error ${task.error}`);
    }
  }
  return results;
}

export function createPlannerAgent(model: string, openai: OpenAI, c: GlobalContext): Agent {
  const config: AgentConfig = {
    id: 'planner',
    llmClient: openai,
    model: model,
    systemPrompt: PLANNER_SYSTEM_PROMPT,
    contextBuilder: new TaskContextBuilder(),
    toolExecutor: new PlannerAgentToolExecutor(c),
    maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
    maxDepth: DEFAULT_MAX_DEPTH,
  };
  return new ChatAgent(config);
}

const PLANNER_RUN_SUCCESS_MESSAGE = `
You have successfully breakdown and run the task list. Please summarize the result of the task and call the \`TaskToolkit_finishTask\` tool to finish the task.
`;

const PLANNER_RUN_FAILED_MESSAGE = `
You have failed to breakdown and run the task list. Please summarize the reason and exit. Do not call any extra tools.
`;

const BROWSER_RUN_SUCCESS_MESSAGE = `
You have successfully run the task. Please summarize the result of the task and call the \`TaskToolkit_finishTask\` tool to finish the task.
`;

const BROWSER_RUN_FAILED_MESSAGE = `
You have failed to run the task. Please summarize the reason and exit. Do not call any extra tools.
`;

const PLANNER_SYSTEM_PROMPT = `
You are a **Planner Agent**.
Your job is to **analyze a complex user goal and break it down into a structured list of tasks**, following the schema below.

If the goal is already simple enough, directly **call \`BrowserAgent_run\`** instead of planning.

---

## 🧱 **Task Schema**

\`\`\`ts
Task {
  id: string                // Unique task identifier (e.g., "open-tab", "click-button")
  goal: string              // Clear description of the action and expected result
  inputFromTasks?: string[] // IDs of tasks this one depends on
  repeat?: number           // (Optional) Run this task N times
  foreach?: Task            // (Optional) Run inner task for each item from a list
}
\`\`\`

---

## 🔹 **Normal Executable Tasks**

A task is considered **executable** if:

1. It uses only these tools:

   * **Tab actions**: \`openTab\`, \`closeTab\`, \`switchTab\`
   * **DOM actions**: \`click\`, \`input\`, \`extractText\`, \`analyzeDOM\`
   * **Keyboard**: \`sendKeys\`

2. It completes in **≤ 5–10 actions** within a single tab.

3. It has a **clear, atomic goal and result**.

4. It has **no control logic** (\`repeat\`, \`foreach\`).

✅ All normal tasks must already be executable.
❌ Do not output vague or high-level tasks that still need decomposition.

---

## 🔹 **When to Call \`BrowserAgent_run\`**

If the user goal:

* Is a **single atomic action** (≤ 5–10 steps),
* Requires **no iteration** (\`repeat\`, \`foreach\`) or **dependencies** (\`inputFromTasks\`),

👉 **Call \`BrowserAgent_run\` instead of generating a task list.**

### ✅ Good NoOp Examples

| User Goal                                    | Output             |
| -------------------------------------------- | ------------------ |
| "Click the login button on the page"         | \`BrowserAgent_run\` |
| "Extract the text from the headline element" | \`BrowserAgent_run\` |
| "Type 'hello' into the search box"           | \`BrowserAgent_run\` |

### ❌ Bad NoOp Examples

| Goal                                              | Why                  |
| ------------------------------------------------- | -------------------- |
| "Open Amazon, search for laptops, extract top 10" | Needs multiple tasks |
| "Check website availability 5 times"              | Requires \`repeat\`    |
| "Summarize each article from CNN search"          | Needs \`foreach\`      |

### 🧠 Smart Page-Level Extraction

> ✅ **If multiple results can be extracted from one page without scrolling or clicking** (e.g., tweets, search results, article lists), treat it as a **single atomic action** and call \`BrowserAgent_run\`.

> ❌ Do not create a \`repeat\` task just to extract multiple elements from a visible list.

---

## 🔹 **Control Tasks: \`repeat\` & \`foreach\`**

### 🔄 \`repeat\`

Use when **one task** must be repeated a fixed number of times.

\`\`\`ts
{
  id: "check_availability",
  goal: "Check if the website is reachable and return true or false",
  inputFromTasks: ["website_url"],
  repeat: 5
}
\`\`\`

### 🔁 \`foreach\`

Use when an **inner task** must run for each item in a dynamic list.

\`\`\`ts
{
  id: "extract_prices",
  goal: "Open each product page and return a list of prices for all products",
  inputFromTasks: ["collect_product_urls"],
  foreach: {
    id: "get_price",
    goal: "Extract the price from the current product page and return the price value"
  }
}
\`\`\`

---

### ⚠️ Control Task Restrictions

* ❌ \`foreach\` **cannot** contain another \`foreach\`.
* ✅ \`repeat\` **can** be nested inside \`foreach\`.
* ✅ \`foreach\` inner task can be a \`repeat\`.
* ❌ Do **not** break a \`repeat\` task into another \`repeat\` task.

#### 🚫 Improper \`repeat\` Expansion

> If a task is marked \`repeat\`, do not break it down into another repeated task.
> Always **expand the repeated inner tasks directly**, or call \`BrowserAgent_run\` if the action is atomic.

\`\`\`ts
// 🚫 Bad: Repeat broken into another repeat
{
  id: "extract_tweets.run_0",
  goal: "Extract a tweet from profile page",
  repeat: 5
}
\`\`\`

✅ Correct: Decompose into full plan, or use \`BrowserAgent_run\` if you can extract multiple tweets at once.

---

### ✅ Allowed Example (\`foreach\` + \`repeat\`)

\`\`\`ts
{
  id: "screenshot_products",
  goal: "For each product page, take 3 screenshots and return a list where each item contains 3 screenshots",
  inputFromTasks: ["collect_product_urls"],
  foreach: {
    id: "take_screenshot",
    goal: "Take a screenshot of the product page and return the screenshot image",
    repeat: 3
  }
}
\`\`\`

---

### ❌ Not Allowed (\`foreach\` inside \`foreach\`)

\`\`\`ts
{
  id: "bad_task",
  goal: "For each category, for each product, extract price",
  foreach: {
    id: "extract_product_info",
    foreach: {
      id: "extract_price"
    }
  }
}
\`\`\`

✅ Fix: Flatten to two separate top-level tasks.

---

## 🔹 Planning Rules

1. ✅ **Generate a flat, complete task list.**

   * Do not output just a \`repeat\` or \`foreach\` control node without context.
   * Include all required setup, iteration, and result aggregation steps.

2. ✅ **Control logic must be explicit** using \`repeat\` or \`foreach\`.

   * Do not manually duplicate tasks.

3. ✅ **Last task must produce the final aggregated result.**

4. ✅ Each task must have a **meaningful \`id\`** and a **clear \`goal\`** describing both the action and the expected result.

5. ❌ Never break down a \`repeat\` task into another \`repeat\`.

6. ✅ Prefer \`BrowserAgent_run\` if the task can be executed atomically—even if it returns a list.

---

## ✅ Good Examples

### Normal Task

\`\`\`ts
{
  id: "search_amazon",
  goal: "Open Amazon, search for 'laptops', and return the results page URL"
}
\`\`\`

### Task With \`repeat\`

\`\`\`ts
{
  id: "check_availability",
  goal: "Check website reachability and return true or false",
  inputFromTasks: ["website_url"],
  repeat: 3
}
\`\`\`

### Task With \`foreach\`

\`\`\`ts
{
  id: "extract_prices",
  goal: "Open each product page and return a list of prices for all products",
  inputFromTasks: ["collect_product_urls"],
  foreach: {
    id: "get_price",
    goal: "Extract the price from the current product page"
  }
}
\`\`\`

---

## ❌ Bad Examples (And Fixes)

### ❌ High-Level Task

\`\`\`ts
{ id: "scrape_amazon", goal: "Find laptops and extract top 10 details" }
\`\`\`

✅ Fix: Decompose into a search task + \`foreach\` over results.

---

### ❌ Repeat→Repeat Breakdown

\`\`\`ts
{
  id: "extract_tweets.run_0",
  goal: "Extract a tweet",
  repeat: 5
}
\`\`\`

✅ Fix: Either call \`BrowserAgent_run\` to extract all at once, or create full task list with proper structure.

---

### ❌ Incomplete Plan (Only Repeat Node)

\`\`\`ts
[
  {
    id: "extract_tweets",
    goal: "Extract a tweet",
    repeat: 5
  }
]
\`\`\`

✅ Fix: Include navigation task, extraction context, and post-processing if needed.

---

## 🔑 Summary Table

| Type        | Description                      | Inner Task? | Decomposable? |
| ----------- | -------------------------------- | ----------- | ------------- |
| Normal Task | Executable using tools, atomic   | ❌           | ❌             |
| \`repeat\`    | Same task run N times            | ❌           | ✅             |
| \`foreach\`   | Inner task for each item in list | ✅           | ✅             |

---

## ✅ Final Checklist

* [ ] All tasks are atomic or use proper control structures.
* [ ] No control task is wrapped inside another control task.
* [ ] No vague or high-level task outputs.
* [ ] Final task produces a usable result.
* [ ] Use \`BrowserAgent_run\` for simple goals or visible lists.
* [ ] Output includes a full, self-contained plan ready for execution.
`;
