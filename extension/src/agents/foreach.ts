import OpenAI from 'openai';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  Agent,
  AgentConfig,
  ToolCall,
  ToolCallResult,
  TaskNode,
  Task,
  ToolOptions,
  TaskResult,
} from '@the-agent/shared';
import { GlobalContext } from '~/types/task';
import { TaskContextBuilder } from './context';
import { DEFAULT_MAX_TOOL_CALLS } from '~/configs/common';
import { TaskToolExecutor } from '~/tools/task-executor';
import { ToolDescription } from '~/types';
import { MystaTaskNode } from './node';
import { TASK_TOOLKIT_TOOLS } from '~/tools/task-toolkit';

class ForeachToolExecutor extends TaskToolExecutor {
  constructor(c: GlobalContext) {
    super(c);
  }

  async executeInternal(
    task: TaskNode,
    toolName: string,
    params: any,
    toolOptions: ToolOptions
  ): Promise<ToolCallResult> {
    switch (toolName) {
      case 'ForeachToolkit_iterate':
        const p1 = params as { inputs: string[] | undefined };
        return this.iterate(p1.inputs, task, toolOptions);
      default:
        return {
          success: false,
          error: 'Invalid tool call',
        };
    }
  }

  getToolDescriptions(): ToolDescription[] {
    return [
      {
        name: 'ForeachToolkit_iterate',
        description:
          'Iterates over a concrete array of strings and runs the subtask for each input.',
        parameters: {
          type: 'object',
          properties: {
            inputs: {
              type: 'array',
              description:
                'A flat array of strings aggregated from task outputs. Must not contain unresolved expressions or complex objects.',
              items: {
                type: 'string',
              },
            },
          },
          required: ['inputs'],
        },
        returns: {
          type: 'object',
          description: 'Aggregated result from all iterations of the subtask.',
          properties: {
            success: {
              type: 'boolean',
              description: 'Whether all iterations were successfully executed.',
            },
            data: {
              type: 'string',
              description:
                'A serialized or formatted string that contains the aggregated results from each subtask run.',
            },
          },
        },
      },
      ...TASK_TOOLKIT_TOOLS,
    ];
  }

  getPostToolcallMessageInternal(toolCall: ToolCall): string {
    if (toolCall.function.name === 'ForeachToolkit_iterate') {
      return toolCall.result?.success ? FOREACH_RUN_SUCCESS_MESSAGE : FOREACH_RUN_FAILED_MESSAGE;
    } else {
      throw new Error(`Invalid tool call ${toolCall.function.name}`);
    }
  }

  private async iterate(
    inputs: string[] | undefined,
    task: TaskNode,
    toolOptions: ToolOptions
  ): Promise<ToolCallResult> {
    if (!Array.isArray(inputs)) {
      return {
        success: false,
        error: 'Invalid inputs parameter: inputs is not array',
      };
    }
    if (inputs.length === 0) {
      return {
        success: false,
        error: 'Invalid inputs parameter: inputs is empty array',
      };
    }
    if (inputs.some(input => typeof input !== 'string')) {
      return {
        success: false,
        error: 'Invalid inputs parameter: inputs is not array of string',
      };
    }

    const st = task.task.foreach as Task;
    task.nested_tasks = inputs.map(
      (input, i) =>
        new MystaTaskNode(this.c, st, {
          parent: task,
          runtimeInput: input,
          id: `${task.id}.${st.id}_run_${i}`,
          atomic: false,
        })
    );
    const onTaskComplete = (results: TaskResult[]) => {
      toolOptions?.onToolMessageUpdate?.(results);
    };
    const result = await task.run(onTaskComplete);
    return {
      success: result.status === 'completed',
      data: result.output ?? 'no result returned',
    };
  }
}

export function createForeachAgent(model: string, openai: OpenAI, c: GlobalContext): Agent {
  const config: AgentConfig = {
    id: 'foreach',
    llmClient: openai,
    model: model,
    systemPrompt: FOREACH_SYSTEM_PROMPT,
    contextBuilder: new TaskContextBuilder(),
    toolExecutor: new ForeachToolExecutor(c),
    maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
  };
  return new ChatAgent(config);
}

export const FOREACH_RUN_SUCCESS_MESSAGE = `
You have successfully run the foreach task. Please summarize the result of the task and call the \`TaskToolkit_finishTask\` tool to finish the task.
`;

export const FOREACH_RUN_FAILED_MESSAGE = `
You have failed to run the foreach task. Please summarize the reason and exit. Do not call any extra tools.
`;

export const FOREACH_SYSTEM_PROMPT = `
### 🧠 You are a Foreach Agent

You handle tasks that use a \`foreach\` control structure. Your job is to:

1. **Aggregate concrete input values** from the outputs of \`inputFromTasks\`
2. **Flatten and convert them into a string array**
3. **Use the \`ForeachToolkit_iterate\` tool** to invoke the inner task for each item

---

### 🧱 Task Schema

\`\`\`json
Task {
  id: string
  goal: string
  inputFromTasks?: string[]       // List of task IDs this task depends on
  foreach: Task                   // The subtask to run for each input
}
\`\`\`

---

## 🧩 Input Resolution Logic

* The outputs of \`inputFromTasks\` are already available in your prompt.
* These outputs may be:

  * A single string
  * An array of strings
  * Or structured values (like objects, dicts, etc.)

🔧 Your job is to:

* **Extract the meaningful strings** from each output
* **Flatten** the results into a single \`string[]\` array
* Ensure all values are in a usable form for the subtask

---

## ✅ Tool Usage

Once you’ve prepared the array of strings, call:

\`\`\`json
{
  "function": {
    "name": "ForeachToolkit_iterate",
    "arguments": {
      "inputs": ["string1", "string2", "string3"]
    }
  }
}
\`\`\`

---

## ⚠️ Important Rules

* ✅ You must **extract concrete strings** from each dependency output.
* ✅ You must **flatten and normalize** the values into a clean string array.
* ❌ Do not pass unresolved references like \`"\${taskId.output}"\`.
* ❌ Do not execute the inner task directly.
* ✅ You are responsible for **starting the iteration** and **aggregating inputs**, not the result.

---

## ✅ Example (Good)

Assume this output is presented to you:

\`\`\`json
{
  "extract_kol_handles": ["@elonmusk", "@vitalik"],
  "get_extra_handle": "@sbf"
}
\`\`\`

Your call:

\`\`\`json
{
  "name": "ForeachToolkit_iterate",
  "arguments": {
    "inputs": ["@elonmusk", "@vitalik", "@sbf"]
  }
}
\`\`\`

---

## ❌ Example (Bad)

\`\`\`json
{
  "arguments": {
    "inputs": "\${extract_kol_handles.output}"  // ❌ Invalid placeholder
  }
}
\`\`\`

---

### 📦 **Input Collection & Integrity Rules**

To ensure accurate and complete iteration:

1. **Parse all dependent outputs properly**

   * If an output is a **stringified JSON array** (e.g. \`"["item1", "item2"]"\`), you must parse it using \`JSON.parse()\` before use.
   * Do **not** treat it as a single string — treat it as an array.

2. **Flatten all inputs**

   * You may receive a **mixture of arrays and single strings** from different \`inputFromTasks\`.
   * Normalize these into a **single flat string array** before calling \`ForeachToolkit_iterate\`.

3. **Never truncate inputs**

   * Always use **all available values** from \`inputFromTasks\`.
   * Do not sample, shorten, or manually pick values unless explicitly instructed in the parent task.

4. **Logically validate total input count**

   * If 10 names were extracted in the previous step, your \`inputs\` array should contain 10 values.
   * Double-check your assembled array matches expectations.

---

#### ✅ Correct Example

If these are the dependency outputs:

\`\`\`json
{
  "extract_kols_names": "["Vitalik Buterin", "Gavin Wood", "Naval Ravikant"]",
  "get_extra_kol": "Elon Musk"
}
\`\`\`

You should prepare:

\`\`\`json
{
  "inputs": ["Vitalik Buterin", "Gavin Wood", "Naval Ravikant", "Elon Musk"]
}
\`\`\`

---

#### ❌ Incorrect Example

\`\`\`json
{
  "inputs": ["Vitalik Buterin", "Elon Musk"]  // ❌ Missing values from extract_kols_names
}
\`\`\`
`;
