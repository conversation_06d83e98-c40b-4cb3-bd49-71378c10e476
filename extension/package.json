{"name": "mysta-agent", "displayName": "<PERSON><PERSON>", "version": "0.2.1", "description": "Mysta - Let AI Run the Web for You.", "manifest": {"permissions": ["storage", "sidePanel", "tabs", "activeTab", "scripting", "debugger"], "host_permissions": ["<all_urls>"], "web_accessible_resources": [{"resources": ["sidepanel.html", "popup.html", "*.js", "*.css"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self' http://localhost; object-src 'self';"}}, "scripts": {"dev": "plasmo dev", "build": "plasmo build --no-minify --source-maps", "watch": "plasmo watch", "package": "plasmo package", "test": "jest"}, "dependencies": {"@cryptography/aes": "^0.1.1", "@heroicons/react": "^2.1.1", "@plasmohq/messaging": "^0.7.1", "@plasmohq/storage": "^1.15.0", "@the-agent/shared": "workspace:*", "@the-agent/telegram": "workspace:*", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/turndown": "^5.0.5", "antd": "^5.25.1", "async-mutex": "^0.5.0", "big-integer": "github:painor/BigInteger.js", "buffer": "^6.0.3", "croppie": "^2.6.5", "crypto-js": "^4.2.0", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "dotenv": "^16.0.3", "emoji-data-ios": "git+https://github.com/korenskoy/emoji-data-ios#de1a69aff2c8eb7548df4e6bcf50c7d2304792fc", "idb-keyval": "^6.2.1", "imurmurhash": "^0.1.4", "lowlight": "^2.9.0", "lucide-react": "^0.509.0", "mp4box": "^0.5.2", "opus-recorder": "github:Ajaxy/opus-recorder", "os-browserify": "^0.3.0", "pako": "^2.1.0", "path-browserify": "^1.0.1", "property-information": "^7.1.0", "qr-code-styling": "github:zubiden/qr-code-styling#dbbfed0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-qr-code": "^2.0.18", "react-router-dom": "^7.8.0", "remark-gfm": "^3.0.1", "turndown": "^7.2.0", "uuid": "^9.0.1", "v8-compile-cache": "^2.4.0", "webextension-polyfill": "^0.10.0", "zustand": "^5.0.6"}, "devDependencies": {"@types/chrome": "^0.0.248", "@types/crypto-js": "^4.2.2", "@types/imurmurhash": "^0.1.4", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/node": "^20.11.0", "@types/uuid": "^9.0.7", "@types/webextension-polyfill": "^0.10.7", "autoprefixer": "^10.4.21", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^23.2.0", "plasmo": "^0.90.3", "postcss": "^8.5.3", "ts-jest": "^29.3.4", "typescript": "^5.3.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "transform": {"^.+\\.tsx?$": ["ts-jest", {"tsconfig": "tsconfig.json"}]}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}}